import json
import asyncio
import logging
from app.agent.agent_creation import create_agent
from langchain_community.callbacks import get_openai_callback
from app.core.senitel_filter import <PERSON><PERSON>ilter
from app.memory.memory_manager import search_memories, store_user_message, format_memory_context
from app.logging_custom_directory.logger_custom import logger

# history = []

def filter_history(history, max_length=6):
       """Keep only the most recent history items"""
       return history[-max_length:] if len(history) > max_length else history



async def generating_reply(
    task, symbol, user_id, report, deep_search, history, chat_id=None, verbose=False
) -> None:
    research_team = await create_agent(symbol, report, deep_search=deep_search)
    # Use chat_id in the thread_id if provided, otherwise use user_id
    thread_id = chat_id if chat_id else user_id
    logger.info(f"Using thread_id: {thread_id}")
    config = {"configurable": {"thread_id": thread_id}}

    # Search for relevant memories
    try:
        memory_results = await search_memories(
            user_id=user_id,
            query=task,
            limit=3,
            include_conversation_history=True,
            chat_id=thread_id
        )

        # Log memory search results
        logger.info(f"Found {len(memory_results.get('results', []))} memories for user {user_id}")

        # Format memories for inclusion in the prompt
        memory_context = format_memory_context(memory_results, include_answers=True)
    except Exception as e:
        logger.error(f"Error searching memories: {e}")
        memory_context = ""

    # try:
    #     # Use chat_id in the state file name if provided
    #     state_file = f"assistant_state_{thread_id}.json"
    #     with open(state_file, "r") as f:
    #         state = json.load(f)
    #     #await research_team.load_state(state)
    # except:
    #     logger.info("No previous state found, starting fresh")

    # Filter history
    filtered_history = filter_history(history)

    # Create prompt with memory context
    if memory_context:
        prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n{memory_context}"
    else:
        prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n"

    state = {
        "input": f"{prompt}"
    }

    # Invoke the agent
    with get_openai_callback() as cb:
        result = await research_team.ainvoke(state, config=config)
        prompt_token = cb.prompt_tokens
        completion_token = cb.completion_tokens
        logger.info(f"Tokens used - Prompt: {prompt_token}, Completion: {completion_token}")

    # Store the conversation in memory
    try:
        # Format messages for storage
        messages = [
            {"role": "user", "content": task},
            {"role": "assistant", "content": result["output"]}
        ]

        # Store the messages asynchronously
        asyncio.create_task(store_user_message(
            user_id=user_id,
            chat_id=thread_id,
            messages=messages,
            run_async=True
        ))
        logger.info(f"Stored conversation in memory for user {user_id}, chat {thread_id}")
    except Exception as e:
        logger.error(f"Error storing conversation in memory: {e}")

    return result, prompt_token, completion_token


class ReplyStream:
    def __init__(self):
        self.full_message_list = {"messages": []}
        self.completion_token = 0
        self.prompt_token = 0
        self.final_response = ""


    async def generate(self, task, symbol, user_id, report, deep_search, history, chat_id=None, verbose=False):
        research_team = await create_agent(symbol, report, deep_search=deep_search)
        # Use chat_id in the thread_id if provided, otherwise use user_id
        thread_id = chat_id if chat_id else user_id
        logger.info(f"Streaming with thread_id: {thread_id}")
        config = {"configurable": {"thread_id": thread_id}}

        # Search for relevant memories
        try:
            memory_results = await search_memories(
                user_id=user_id,
                query=task,
                limit=3,
                include_conversation_history=True,
                chat_id=thread_id
            )

            # Log memory search results
            logger.info(f"Found {len(memory_results.get('results', []))} memories for user {user_id}")

            # Format memories for inclusion in the prompt (now includes Q&A)
            memory_context = format_memory_context(memory_results, include_answers=True)
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            memory_context = ""

        # Create prompt with memory context
        if memory_context:
            prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n{memory_context}"
        else:
            prompt = f"user message: {task} \n\n user symbol: {symbol} \n\n"

        state = {
            "input": f"{prompt}",
        }

        try:
            async for msg, metadata in research_team.astream(input=state, stream_mode="messages", config=config):
                if "writer_node" in metadata["langgraph_checkpoint_ns"]:
                    # Accumulate the response for storing in memory later
                    self.final_response += msg.content
                    yield(msg.content)

            self.prompt_token = 0
            self.completion_token = 0

            # Store the conversation in memory after streaming is complete
            if self.final_response and user_id and thread_id:
                try:
                    # Format messages for storage
                    messages = [
                        {"role": "user", "content": task},
                        {"role": "assistant", "content": self.final_response}
                    ]

                    # Store the messages asynchronously
                    asyncio.create_task(store_user_message(
                        user_id=user_id,
                        chat_id=thread_id,
                        messages=messages,
                        run_async=True
                    ))
                    logger.info(f"Stored streamed conversation in memory for user {user_id}, chat {thread_id}")
                except Exception as e:
                    logger.error(f"Error storing streamed conversation in memory: {e}")

        except Exception as e:
            error_msg = f"[ERROR]: {str(e)}"
            logger.error(error_msg)
            yield error_msg


