from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import generate_reply
from app.memory.init_qdrant import init_qdrant_collection
from app.logging_custom_directory.logger_custom import logger

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan events for the FastAPI application."""
    # Startup: Initialize services
    try:
        # Initialize Qdrant collection for memory storage
        init_qdrant_collection()
        logger.info("Qdrant collection initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing Qdrant collection: {e}")

    yield

    # Shutdown: Clean up resources
    logger.info("Shutting down application")

app = FastAPI(title="TraderGPT with LangMem", lifespan=lifespan)

# ✅ Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # ⚠️ Replace with specific origins in production
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(generate_reply.router)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "memory_system": "LangMem"}
