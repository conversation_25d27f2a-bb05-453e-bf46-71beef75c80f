# Memory Management System with LangMem

This module provides memory management capabilities for the TraderGPT application using LangMem for embeddings and Qdrant for vector storage.

## Overview

The memory system is designed to:

1. Store and retrieve conversation history
2. Provide relevant context for future conversations
3. Use vector search to find relevant memories

## Components

### Memory Manager

The memory manager provides the core functionality for memory management:

- `get_memory_store`: Get a Qdrant client instance for vector storage
- `get_embeddings`: Get embeddings for text using LangMem's utility function
- `store_user_message`: Store a user message in memory
- `search_memories`: Search for relevant memories

### Configuration

Memory configuration is defined in `langmem_config.py`:

- `QDRANT_URL`: URL for the Qdrant vector database
- `QDRANT_COLLECTION_NAME`: Name of the Qdrant collection for storing memories

## Integration

The memory system is integrated with the agent system in the following ways:

1. In `agent_creation.py`, the `writer_node` function searches for relevant memories and includes them in the prompt.
2. In `calling_agent.py`, the `generating_reply` function searches for relevant memories and includes them in the prompt.
3. Both functions store the conversation in memory after generating a response.

## Usage

### Storing User Messages

```python
from app.memory import store_user_message

messages = [
    {"role": "user", "content": "What do you think about Tesla stock?"},
    {"role": "assistant", "content": "Tesla is a leading electric vehicle manufacturer..."}
]

await store_user_message(
    user_id="user123",
    chat_id="chat456",
    messages=messages,
    run_async=True
)
```

### Searching Memories

```python
from app.memory import search_memories

memory_results = await search_memories(
    user_id="user123",
    query="Tesla stock",
    limit=3,
    include_conversation_history=True,
    chat_id="chat456"
)
```

## Testing

Use the `test_langmem_direct.py` script to test the memory system:

```bash
python test_langmem_direct.py
```

## Initialization

Before using the memory system, initialize the Qdrant collection:

```python
from app.memory.init_qdrant import init_qdrant_collection

init_qdrant_collection()
```
