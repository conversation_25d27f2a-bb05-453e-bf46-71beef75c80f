"""
Test script for the sliding window memory management functionality.
"""
import asyncio
import datetime
import logging
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.memory.memory_manager import store_user_message, search_memories, get_chat_memory_count, get_memory_store
from app.memory.init_qdrant import init_qdrant_collection
from app.memory.langmem_config import MAX_MEMORIES_PER_CHAT
from app.logging_custom_directory.logger_custom import logger

# Set up logging
logging.basicConfig(level=logging.INFO)

async def test_sliding_window():
    """
    Test the sliding window functionality by storing more than MAX_MEMORIES_PER_CHAT messages
    and verifying that only the most recent ones are retained.
    """
    print(f"Testing sliding window with MAX_MEMORIES_PER_CHAT = {MAX_MEMORIES_PER_CHAT}")
    
    # Initialize Qdrant collection
    init_qdrant_collection()
    
    # Create test user and chat IDs
    user_id = "test_sliding_window_user"
    chat_id = f"test_chat_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    print(f"Using user_id: {user_id}, chat_id: {chat_id}")
    
    # Get Qdrant client
    client = get_memory_store()
    if not client:
        print("Warning: Qdrant client not available, testing with fallback store only")
    
    # Store messages exceeding the sliding window limit
    num_messages_to_store = MAX_MEMORIES_PER_CHAT + 5  # Store 5 more than the limit
    print(f"Storing {num_messages_to_store} messages...")
    
    stored_messages = []
    for i in range(num_messages_to_store):
        message_content = f"Test message {i+1}: What is the current price of AAPL stock?"
        response_content = f"Response {i+1}: AAPL is currently trading at $150.{i:02d}"
        
        messages = [
            {"role": "user", "content": message_content},
            {"role": "assistant", "content": response_content}
        ]
        
        stored_messages.append((message_content, response_content))
        
        # Store the message
        success = await store_user_message(
            user_id=user_id,
            chat_id=chat_id,
            messages=messages,
            run_async=False  # Wait for completion to ensure proper testing
        )
        
        if success:
            print(f"✓ Stored message {i+1}")
        else:
            print(f"✗ Failed to store message {i+1}")
        
        # Small delay to ensure different timestamps
        await asyncio.sleep(0.1)
    
    # Wait a bit for all operations to complete
    await asyncio.sleep(2)
    
    # Check memory count if using Qdrant
    if client:
        try:
            memory_count = await get_chat_memory_count(client, user_id, chat_id)
            print(f"\nMemory count in Qdrant: {memory_count}")
            
            if memory_count <= MAX_MEMORIES_PER_CHAT:
                print(f"✓ Sliding window working: {memory_count} <= {MAX_MEMORIES_PER_CHAT}")
            else:
                print(f"✗ Sliding window failed: {memory_count} > {MAX_MEMORIES_PER_CHAT}")
        except Exception as e:
            print(f"Error checking memory count: {e}")
    
    # Test search functionality
    print(f"\nTesting search functionality...")
    try:
        search_results = await search_memories(
            user_id=user_id,
            query="AAPL stock price",
            limit=20,  # Request more than the sliding window limit
            include_conversation_history=True,
            chat_id=chat_id
        )
        
        results = search_results.get("results", [])
        print(f"Search returned {len(results)} results")
        
        if len(results) <= MAX_MEMORIES_PER_CHAT:
            print(f"✓ Search respects sliding window: {len(results)} <= {MAX_MEMORIES_PER_CHAT}")
        else:
            print(f"✗ Search doesn't respect sliding window: {len(results)} > {MAX_MEMORIES_PER_CHAT}")
        
        # Check if results are ordered by recency (most recent first)
        if len(results) > 1:
            timestamps = [r["metadata"].get("timestamp", "") for r in results]
            is_ordered = all(timestamps[i] >= timestamps[i+1] for i in range(len(timestamps)-1))
            
            if is_ordered:
                print("✓ Results are ordered by recency (most recent first)")
            else:
                print("✗ Results are not properly ordered by recency")
        
        # Display the most recent few results
        print(f"\nMost recent {min(3, len(results))} memories:")
        for i, result in enumerate(results[:3]):
            memory_content = result.get("memory", "")
            timestamp = result["metadata"].get("timestamp", "")
            print(f"  {i+1}. {memory_content[:50]}... (timestamp: {timestamp})")
        
        # Verify that the oldest messages are no longer present
        if len(stored_messages) > MAX_MEMORIES_PER_CHAT:
            oldest_messages = stored_messages[:len(stored_messages) - MAX_MEMORIES_PER_CHAT]
            newest_messages = stored_messages[-MAX_MEMORIES_PER_CHAT:]
            
            # Check if any of the oldest messages are still in results
            oldest_found = False
            for old_msg, _ in oldest_messages:
                for result in results:
                    if old_msg in result.get("memory", ""):
                        oldest_found = True
                        break
                if oldest_found:
                    break
            
            if not oldest_found:
                print("✓ Oldest messages successfully removed from storage")
            else:
                print("✗ Some oldest messages still found in storage")
            
            # Check if the newest messages are present
            newest_found_count = 0
            for new_msg, _ in newest_messages:
                for result in results:
                    if new_msg in result.get("memory", ""):
                        newest_found_count += 1
                        break
            
            print(f"✓ Found {newest_found_count}/{len(newest_messages)} of the newest messages")
        
    except Exception as e:
        print(f"Error during search test: {e}")
    
    print(f"\nSliding window test completed!")

if __name__ == "__main__":
    asyncio.run(test_sliding_window())
