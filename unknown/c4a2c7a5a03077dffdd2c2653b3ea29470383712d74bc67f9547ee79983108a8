from typing import Optional

from pydantic import BaseModel, Field


class UserAskRequest(BaseModel):
    task: str = Field(..., description="The task or question the user is asking")
    symbol: str = Field(..., description="The stock symbol to fetch data for")
    chat_id: Optional[str] = Field(
        None,
        description="The chat ID to identify which conversation this message belongs to"
    )
    report: Optional[bool] = Field(
            False,
            description="Whether the user wants the full report or just the answer",
        )
    deep_search: Optional[bool] = Field(
        False,
        description="Whether the user wants to have deep search option enabled.",
    )
